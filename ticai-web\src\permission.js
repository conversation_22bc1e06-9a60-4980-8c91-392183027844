import router from './router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from './utils/auth'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login'] // no redirect whitelist

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  const user = store.getters.user
  var moduleName = (to.meta ? to.meta.name : null) || to.name || ''
  if (to.meta && to.meta.title) {
    if (to.path === '/formDesigner') document.title = to.meta.title + '-' + to.query.name
    else document.title = to.meta.title
  }
  if (user && user.id) {
    store.dispatch('app/setModuleName', moduleName)
    next()
  } else {
    if (whiteList.indexOf(to.path) !== -1) { // 白名单忽略
      next()
    } else {
      NProgress.done()
      const token = getToken()
      if (token) {
        store.dispatch('user/get', token).then(r => {
          if (r.code > 0) {
            store.dispatch('app/setModuleName', moduleName)
            next()
          } else {
            next(`/login?redirect=${to.path}`)
          }
        }).catch(() => {
          next(`/login?redirect=${to.path}`)
        })
      } else next(`/login?redirect=${to.path}`)
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
