<template>
  <div>
    <el-dialog v-dialog-drag title="调拨申请" width="900px" :visible.sync="visible" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form ref="dataform" size="small" label-width="140px" :model="form" :rules="formRules">
        <el-row>
          <el-col :span="12">
            <el-form-item label="调拨申请日期：" prop="time">
              <el-date-picker v-model="form.time" type="date" placeholder="请选择调拨日期" value-format="yyyy-MM-dd" format="yyyy-MM-dd" clearable editable style="width:200px;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调拨申请人：" prop="time">
              <user-chosen v-model="form.user" type="1" clearable placeholder="请选择调拨申请人" style="width:100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="调入部门/区域：" prop="dept">
          <dept-region-chosen v-model="regions" :simple="false" clearable placeholder="请选择部门/区域" style="width:100%" />
        </el-form-item>
        <el-form-item label="调拨说明：" prop="memo">
          <el-input v-model="form.memo" type="textarea" maxlength="200" show-word-limit clearable placeholder="请输入调拨说明" style="width:100%" />
        </el-form-item>
      </el-form>
      <upload-file v-model="fileList" simple multiple type="DB" />
      <div style="margin-top:20px;">调拨资产明细：</div>
      <el-divider></el-divider>
      <el-table :data="assetList" size="small" border>
        <el-table-column label="资产类型" prop="typeName" width="180" header-align="center" />
        <el-table-column label="资产编码" prop="no" width="120" align="center" />
        <el-table-column label="资产名称" prop="name" min-width="150" />
        <el-table-column label="所属部门" prop="deptName" width="150" header-align="center" />
        <el-table-column label="当前状态" prop="status" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="getAssetStatusType(scope.row)" size="small">{{ getAssetStatusText(scope.row) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="80">
          <template slot="header">
            <el-button type="success" size="mini" @click="addAsset">新 增</el-button>
          </template>
          <template slot-scope="scope">
            <el-link type="danger" size="mini" icon="el-icon-remove" @click.stop="removeAsset(scope.index)">移除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
    <asset-chosen ref="asset" multiple @selected="selectedAsset" />
  </div>
</template>
<script>
import store from '@/store'
import { dateStr } from '@/utils'
import { getAssetStatusType, getAssetStatusText } from '../js/asset.js'

import AssetChosen from '../account/AssetChosen.vue'
import UserChosen from '@/views/components/UserChosen.vue'
import DeptRegionChosen from '@/views/components/DeptRegionChosen.vue'
import UploadFile from '@/views/components/UploadFile.vue'

export default {
  components: { AssetChosen, UserChosen, DeptRegionChosen, UploadFile },
  data() {
    const regionsRule = (rule, value, callback) => {
      if (!this.form.dept) callback('请选择调拨部门/区域')
      else callback()
    }
    return {
      visible: false,
      currDate: dateStr(),
      form: { user: '' }, // 领用申请信息表单
      regions: [],
      formRules: {
        time: [{ required: true, message: '请选择调拨日期', trigger: 'blur' }],
        dept: [{ required: true, validator: regionsRule, trigger: 'blur' }]
      },
      assetList: [],
      fileList: []
    }
  },
  watch: {
    regions: function(nv) {
      this.form.dept = Array.isArray(nv) && nv.length > 0 ? nv[nv.length - 1] : null
      this.form.region = Array.isArray(nv) && nv.length > 0 ? nv[nv.length - 1] : null
    }
  },
  methods: {
    getAssetStatusType(v) {
      return getAssetStatusType(v.status)
    },
    getAssetStatusText(v) {
      return getAssetStatusText(v.status)
    },
    show() {
      this.visible = true
      this.form = { time: dateStr(), user: store.getters.user.id }
      this.assetList = []
      this.fileList = []
      this.regions = []
    },
    addAsset() {
      this.$refs.asset.show({ status: '1' })
    },
    selectedAsset(items) {
      const ids = { }
      this.assetList.forEach(r => { ids[r.id] = true })
      items.forEach(r => {
        if (!ids[r.id]) this.assetList.push(r)
      })
    },
    removeAsset(index) {
      this.$confirm('确定要移除吗?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {
        this.assetList.splice(index, 1)
      }).catch(() => {
      })
    },
    save() {
      this.$refs.dataform.validate(valid => {
        if (valid) {
          const details = []
          this.assetList.forEach(r => details.push({ asset: r.id, dept: r.dept, region: r.region, location: r.location, useDept: r.userDept, useUser: r.useUser, assetStatus: r.status }))
          if (!details.length) return this.$message.warning('请选择要调拨的资产')
          this.form.assetList = details
          this.form.fileList = this.fileList
          this.$http({ url: '/am/asset/allocate/apply', data: this.form }).then(res => {
            if (res.code > 0) {
              this.visible = false
              this.$message.success('提交成功')
              this.$emit('success')
            }
          })
        }
      })
    }
  }
}
</script>
