{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\asset\\allocate\\apply.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\asset\\allocate\\apply.vue", "mtime": 1753346197691}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["apply.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "apply.vue", "sourceRoot": "src/views/asset/allocate", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog v-dialog-drag title=\"调拨申请\" width=\"900px\" :visible.sync=\"visible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"dataform\" size=\"small\" label-width=\"140px\" :model=\"form\" :rules=\"formRules\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"调拨申请日期：\" prop=\"time\">\r\n              <el-date-picker v-model=\"form.time\" type=\"date\" placeholder=\"请选择调拨日期\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" clearable editable style=\"width:200px;\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"调拨申请人：\" prop=\"time\">\r\n              <user-chosen v-model=\"form.user\" type=\"1\" clearable placeholder=\"请选择调拨申请人\" style=\"width:100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"调入部门/区域：\" prop=\"dept\">\r\n          <dept-region-chosen v-model=\"regions\" :simple=\"false\" clearable placeholder=\"请选择部门/区域\" style=\"width:100%\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"调拨说明：\" prop=\"memo\">\r\n          <el-input v-model=\"form.memo\" type=\"textarea\" maxlength=\"200\" show-word-limit clearable placeholder=\"请输入调拨说明\" style=\"width:100%\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <upload-file v-model=\"fileList\" simple multiple type=\"DB\" />\r\n      <div style=\"margin-top:20px;\">调拨资产明细：</div>\r\n      <el-divider></el-divider>\r\n      <el-table :data=\"assetList\" size=\"small\" border>\r\n        <el-table-column label=\"资产类型\" prop=\"typeName\" width=\"180\" header-align=\"center\" />\r\n        <el-table-column label=\"资产编码\" prop=\"no\" width=\"120\" align=\"center\" />\r\n        <el-table-column label=\"资产名称\" prop=\"name\" min-width=\"150\" />\r\n        <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"150\" header-align=\"center\" />\r\n        <el-table-column label=\"当前状态\" prop=\"status\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getAssetStatusType(scope.row)\" size=\"small\">{{ getAssetStatusText(scope.row) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"80\">\r\n          <template slot=\"header\">\r\n            <el-button type=\"success\" size=\"mini\" @click=\"addAsset\">新 增</el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-link type=\"danger\" size=\"mini\" icon=\"el-icon-remove\" @click.stop=\"removeAsset(scope.index)\">移除</el-link>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"visible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <asset-chosen ref=\"asset\" multiple @selected=\"selectedAsset\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport store from '@/store'\r\nimport { dateStr } from '@/utils'\r\nimport { getAssetStatusType, getAssetStatusText } from '../js/asset.js'\r\n\r\nimport AssetChosen from '../account/AssetChosen.vue'\r\nimport UserChosen from '@/views/components/UserChosen.vue'\r\nimport DeptRegionChosen from '@/views/components/DeptRegionChosen.vue'\r\nimport UploadFile from '@/views/components/UploadFile.vue'\r\n\r\nexport default {\r\n  components: { AssetChosen, UserChosen, DeptRegionChosen, UploadFile },\r\n  data() {\r\n    const regionsRule = (rule, value, callback) => {\r\n      if (!this.form.dept) callback('请选择调拨部门/区域')\r\n      else callback()\r\n    }\r\n    return {\r\n      visible: false,\r\n      currDate: dateStr(),\r\n      form: { user: '' }, // 领用申请信息表单\r\n      regions: [],\r\n      formRules: {\r\n        time: [{ required: true, message: '请选择调拨日期', trigger: 'blur' }],\r\n        dept: [{ required: true, validator: regionsRule, trigger: 'blur' }]\r\n      },\r\n      assetList: [],\r\n      fileList: []\r\n    }\r\n  },\r\n  watch: {\r\n    regions: function(nv) {\r\n      this.form.region = nv.length > 1 ? nv[1] : null\r\n      this.form.dept = nv.length > 0 ? nv[0] : null\r\n    }\r\n  },\r\n  methods: {\r\n    getAssetStatusType(v) {\r\n      return getAssetStatusType(v.status)\r\n    },\r\n    getAssetStatusText(v) {\r\n      return getAssetStatusText(v.status)\r\n    },\r\n    show() {\r\n      this.visible = true\r\n      this.form = { time: dateStr(), user: store.getters.user.id }\r\n      this.assetList = []\r\n      this.fileList = []\r\n      this.regions = []\r\n    },\r\n    addAsset() {\r\n      this.$refs.asset.show({ status: '1' })\r\n    },\r\n    selectedAsset(items) {\r\n      const ids = { }\r\n      this.assetList.forEach(r => { ids[r.id] = true })\r\n      items.forEach(r => {\r\n        if (!ids[r.id]) this.assetList.push(r)\r\n      })\r\n    },\r\n    removeAsset(index) {\r\n      this.$confirm('确定要移除吗?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        this.assetList.splice(index, 1)\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    save() {\r\n      this.$refs.dataform.validate(valid => {\r\n        if (valid) {\r\n          const details = []\r\n          this.assetList.forEach(r => details.push({ asset: r.id, dept: r.dept, region: r.region, location: r.location, useDept: r.userDept, useUser: r.useUser, assetStatus: r.status }))\r\n          if (!details.length) return this.$message.warning('请选择要调拨的资产')\r\n          this.form.assetList = details\r\n          this.form.fileList = this.fileList\r\n          this.$http({ url: '/am/asset/allocate/apply', data: this.form }).then(res => {\r\n            if (res.code > 0) {\r\n              this.visible = false\r\n              this.$message.success('提交成功')\r\n              this.$emit('success')\r\n            }\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}